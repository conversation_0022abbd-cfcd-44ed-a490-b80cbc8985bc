"use client"

import * as React from "react"
import * as AvatarPrimitive from "@radix-ui/react-avatar"

import { cn } from "@/utilities/ui"

const Avatar: React.FC<
  { ref?: React.Ref<HTMLSpanElement> } & React.ComponentProps<typeof AvatarPrimitive.Root>
> = ({ className, ref, ...props }) => (
  <AvatarPrimitive.Root
    ref={ref}
    className={cn("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full", className)}
    {...props}
  />
)

const AvatarImage: React.FC<
  { ref?: React.Ref<HTMLImageElement> } & React.ComponentProps<typeof AvatarPrimitive.Image>
> = ({ className, ref, ...props }) => (
  <AvatarPrimitive.Image
    ref={ref}
    className={cn("aspect-square h-full w-full", className)}
    {...props}
  />
)

const AvatarFallback: React.FC<
  { ref?: React.Ref<HTMLSpanElement> } & React.ComponentProps<typeof AvatarPrimitive.Fallback>
> = ({ className, ref, ...props }) => (
  <AvatarPrimitive.Fallback
    ref={ref}
    className={cn(
      "flex h-full w-full items-center justify-center rounded-full bg-muted",
      className
    )}
    {...props}
  />
)

export { Avatar, AvatarImage, AvatarFallback }
